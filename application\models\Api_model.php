<?php

class Api_model extends CI_Model {
    
    public function __construct()
    {
        parent::__construct();
    }

    /**
     * Get all airlines
     * @return array
     */
    public function get_airlines()
    {
        $this->db->select('code_airline, name');
        $this->db->from('airlines');
        $this->db->order_by('id_airline', 'ASC');
        $query = $this->db->get();
        
        return $query->result();
    }
    /**
     * Get all airlines
     * @return array
     */
    public function get_airports()
    {
        $this->db->select('*');
        $this->db->from('airports');
        $this->db->order_by('id_airport', 'ASC');
        $query = $this->db->get();
        
        return $query->result();
    }
    
    /**
     * Get all zones
     * @return array
     */
    public function get_zones()
    {
        $this->db->select('*');
        $this->db->from('zones');
        $this->db->order_by('zone', 'ASC');
        $query = $this->db->get();
        
        return $query->result();
    }

    /**
     * Get all rates
     * @return array
     */
    function all_rates() {
        $this->db->where('activo', 1);
        $this->db->order_by('id_rate','Asc');
        $consulta = $this->db->get('rates');
        return $consulta->result();
    }

    /**
     * Get all hotels ordered by name (legacy method - mantener compatibilidad)
     * @return array
     */
    public function get_all_hotels()
    {
        $this->db->select('*');
        $this->db->from('hoteles');
        $this->db->order_by('nombre', 'ASC');
        $query = $this->db->get();
        
        return $query->result();
    }
    
    /**
     * Search hotels with advanced filtering and sorting
     * @param array $params - Array with search parameters
     * @return array
     */
    public function search_hotels($params = array()) {
        try {
            // Start building query
            $this->db->select('h.*, z.zone, z.nombre as zone_name');
            $this->db->from('hoteles h');
            $this->db->join('zones z', 'z.idzones = h.zones_idzones', 'left');
            
            // Apply search filter if provided
            if (!empty($params['search'])) {
                $search_term = trim($params['search']);
                
                // Use LIKE for flexible searching
                $this->db->group_start();
                $this->db->like('h.nombre', $search_term);
                $this->db->or_like('h.address', $search_term);
                $this->db->or_like('z.nombre', $search_term);
                $this->db->or_like('z.zone', $search_term);
                $this->db->group_end();
                
                log_message('info', "Hotels search applied for term: '$search_term'");
            }
            
            // Apply zone filter if provided
            if (!empty($params['zone_id']) && is_numeric($params['zone_id'])) {
                $this->db->where('h.zones_idzones', (int)$params['zone_id']);
            }
            
            // Apply status filter if provided
            if (!empty($params['status'])) {
                switch (strtolower($params['status'])) {
                    case 'active':
                        // Assuming there's a status field or active hotels don't have specific condition
                        // Adjust based on your hotel status logic
                        break;
                    case 'inactive':
                        // Add inactive condition if exists
                        break;
                    case 'all':
                    default:
                        // No filter for status
                        break;
                }
            }
            
            // Apply sorting
            $sort_by = !empty($params['sort_by']) ? $params['sort_by'] : 'nombre';
            $sort_order = !empty($params['sort_order']) ? strtoupper($params['sort_order']) : 'ASC';
            
            // Validate sort_by to prevent SQL injection
            $allowed_sort_fields = array('nombre', 'zone_name', 'address', 'zones_idzones');
            if (in_array($sort_by, $allowed_sort_fields)) {
                if ($sort_by === 'zone_name') {
                    $this->db->order_by('z.nombre', $sort_order);
                } else {
                    $this->db->order_by('h.' . $sort_by, $sort_order);
                }
            } else {
                // Default sort
                $this->db->order_by('h.nombre', 'ASC');
            }
            
            // Apply pagination if provided
            if (!empty($params['limit']) && is_numeric($params['limit'])) {
                $limit = min(100, max(1, (int)$params['limit'])); // Max 100, min 1
                $this->db->limit($limit);
                
                if (!empty($params['page']) && is_numeric($params['page'])) {
                    $page = max(1, (int)$params['page']);
                    $offset = ($page - 1) * $limit;
                    $this->db->offset($offset);
                }
            }
            
            $query = $this->db->get();
            
            // Check for database errors
            if ($this->db->error()['code'] !== 0) {
                log_message('error', 'Database error in search_hotels: ' . $this->db->error()['message']);
                return false;
            }
            
            $results = $query->result();
            
            // Log successful search
            if (!empty($params['search'])) {
                log_message('info', "Hotels search completed. Found " . count($results) . " results for term: '" . $params['search'] . "'");
            }
            
            return $results;
            
        } catch (Exception $e) {
            log_message('error', 'Exception in search_hotels: ' . $e->getMessage());
            return false;
        }
    }
    
    /**
     * Get all active service fleets
     * @return array
     */
    public function get_all_service_fleets()
    {
        $this->db->select('*');
        $this->db->from('service_fleets');
        $this->db->where('activo', 1);
        $this->db->order_by('id_fleet', 'ASC');
        $query = $this->db->get();
        
        return $query->result();
    }
    
    /**
     * Get zones with their hotels count
     * @return array
     */
    public function get_zones_with_hotel_count()
    {
        $this->db->select('z.*, COUNT(h.id_hotel) as hotel_count');
        $this->db->from('zones z');
        $this->db->join('hoteles h', 'h.zones_idzones = z.idzones', 'left');
        $this->db->group_by('z.idzones');
        $this->db->order_by('z.zone', 'ASC');
        $query = $this->db->get();
        
        return $query->result();
    }
    
    /**
     * Get hotels by zone
     * @param int $zone_id
     * @return array
     */
    public function get_hotels_by_zone($zone_id)
    {
        $this->db->select('*');
        $this->db->from('hoteles');
        $this->db->where('zones_idzones', $zone_id);
        $this->db->order_by('nombre', 'ASC');
        $query = $this->db->get();
        
        return $query->result();
    }
    
    /**
     * Get shared rates data
     * @return array
     */
    public function get_shared_rates()
    {
        // Ejemplo de consulta para tarifas compartidas
        // Ajusta según tu estructura de base de datos
        $this->db->select('sf.*, s.service_name');
        $this->db->from('service_fleets sf');
        $this->db->join('services s', 's.id_service = sf.service_id', 'left');
        $this->db->where('sf.activo', 1);
        $this->db->where('sf.shared_rate', 1); // Asumiendo que hay un campo para tarifas compartidas
        $this->db->order_by('sf.id_fleet', 'ASC');
        $query = $this->db->get();
        
        return $query->result();
    }
    
    /**
     * Get all rates with transport/vehicle names
     * @return array
     */
    public function get_all_rates()
    {
        $this->db->select('r.*, sf.name as fleet_name, sf.passengers, sf.service, z.nombre as zone_name');
        $this->db->from('rates r');
        $this->db->join('service_fleets sf', 'sf.id_fleet = r.service_fleets_id_fleet', 'left');
        $this->db->join('zones z', 'z.idzones = r.zones_idzones', 'left');
        $this->db->where('r.activo', 1);
        $this->db->order_by('r.id_rate', 'ASC');
        $query = $this->db->get();
        
        return $query->result();
    }
    
    /**
     * Get rates by zone
     * @param int $zone_id
     * @return array
     */
    public function get_rates_by_zone($zone_id)
    {
        $this->db->select('r.*, sf.name as fleet_name, sf.passengers');
        $this->db->from('rates r');
        $this->db->join('service_fleets sf', 'sf.id_fleet = r.service_fleets_id_fleet', 'left');
        $this->db->where('r.activo', 1);
        $this->db->where('r.zones_idzones', $zone_id);
        $this->db->order_by('r.id_rate', 'ASC');
        $query = $this->db->get();
        
        return $query->result();
    }

    /**
     * Obtiene las tarifas agrupadas por FLEET (vehículo) para una zona específica,
     * consolidando en un solo objeto la tarifa One Way y la Round Trip (si existen).
     * Estructura de retorno (array de arrays asociativos):
     * [
     *   {
     *     zone_id: "1",
     *     fleet_id: "1",
     *     fleet_name: "Chevrolet Suburban",
     *     passengers: "5",
     *     one_way: { rate_id: "1", code: "1POWSUB", price: 80.0 },
     *     round_trip: { rate_id: "11", code: "1PRTSUB", price: 155.0 }
     *   }, ...
     * ]
     * @param int $zone_id
     * @return array
     */
    public function get_grouped_rates_by_zone($zone_id)
    {
        if (!$zone_id) { return array(); }

        // Reutilizamos la consulta existente
        $rates = $this->get_rates_by_zone($zone_id);

        $grouped = array();

        // Mapas para identificar tipo de servicio (fallback usando codeS)
        $oneWayServiceIds = array(5,7);      // IDs detectados en ejemplo para OW
        $roundTripServiceIds = array(6,8);   // IDs detectados en ejemplo para RT

        foreach ($rates as $r) {
            $fleetId = $r->service_fleets_id_fleet;
            if (!isset($grouped[$fleetId])) {
                $grouped[$fleetId] = array(
                    'zone_id'      => (string) $r->zones_idzones,
                    'fleet_id'     => (string) $fleetId,
                    'fleet_name'   => $r->fleet_name,
                    'passengers'   => (string) $r->passengers,
                    'one_way'      => null,
                    'round_trip'   => null,
                );
            }

            $isOneWay = in_array((int)$r->services_id_services, $oneWayServiceIds, true) || strpos($r->codeS, 'OW') !== false; // codeS contiene OW
            $isRoundTrip = in_array((int)$r->services_id_services, $roundTripServiceIds, true) || strpos($r->codeS, 'RT') !== false; // codeS contiene RT

            $rateData = array(
                'rate_id' => (string) $r->id_rate,
                'code'    => $r->codeS,
                'price'   => (float) $r->price
            );

            if ($isOneWay) {
                $grouped[$fleetId]['one_way'] = $rateData;
            } elseif ($isRoundTrip) {
                $grouped[$fleetId]['round_trip'] = $rateData;
            } else {
                // Si no se pudo clasificar, lo dejamos en un arreglo adicional opcional
                $grouped[$fleetId]['other'][] = $rateData;
            }
        }

        // Ordenar por fleet_id manteniendo formato de array indexado
        usort($grouped, function($a, $b) {
            return strcasecmp($a['fleet_id'], $b['fleet_id']);
        });

        return $grouped;
    }
    
    /**
     * Get rates by fleet - Transporter
     * @param int $fleet_id
     * @return array
     */
    public function get_rates_by_fleet($fleet_id)
    {
        $this->db->select('r.*, z.zone as zone_id, z.nombre as zone_name, sf.name as fleet_name');
        $this->db->from('rates r');
        $this->db->join('zones z', 'z.idzones = r.zones_idzones', 'left');
        $this->db->join('service_fleets sf', 'sf.id_fleet = r.service_fleets_id_fleet', 'left');
        $this->db->where('r.activo', 1);
        $this->db->where('r.service_fleets_id_fleet', $fleet_id);
        $this->db->order_by('r.zones_idzones', 'ASC');
        $query = $this->db->get();
        
        return $query->result();
    }
    
    /**
     * Get rates with fleet and zone details
     * @return array
     */
    public function get_rates_detailed()
    {
        $this->db->select('r.*, sf.name as fleet_name, sf.passengers, sf.service, z.name as zone_name');
        $this->db->from('rates r');
        $this->db->join('service_fleets sf', 'sf.id_fleet = r.service_fleets_id_fleet', 'left');
        $this->db->join('zones z', 'z.idzones = r.zones_idzones', 'left');
        $this->db->where('r.activo', 1);
        $this->db->order_by('r.id_rate', 'ASC');
        $query = $this->db->get();
        
        return $query->result();
    }
    
    /**
     * Get fleet services with additional details
     * @return array
     */
    public function get_fleet_services_detailed()
    {
        // The query joins fleets with their services through rates
        $this->db->select('sf.*, s.id_services, s.name as service_name, s.type as service_type');
        $this->db->from('service_fleets sf');
        $this->db->join('rates', 'rates.service_fleets_id_fleet = sf.id_fleet', 'left');
        $this->db->join('services s', 's.id_services = rates.services_id_services', 'left');
        $this->db->where('sf.activo', 1);
        $this->db->order_by('sf.id_fleet', 'ASC');
        $this->db->order_by('s.id_services', 'ASC');
        $query = $this->db->get();
        
        $results = $query->result();
        
        $fleets = array();
        foreach ($results as $row) {
            // If the fleet is not yet in our array, add it
            if (!isset($fleets[$row->id_fleet])) {
                $fleets[$row->id_fleet] = array(
                    'id_fleet' => $row->id_fleet,
                    'id_code' => $row->id_code,
                    'name' => $row->name,
                    'name_esp' => $row->name_esp,
                    'shortname' => $row->shortname,
                    'description' => $row->description,
                    'activo' => $row->activo,
                    'passengers' => $row->passengers,
                    'luggage' => $row->luggage,
                    'imgurl' => $row->imgurl,
                    'imgurl_grande' => $row->imgurl_grande,
                    'service' => $row->service,
                    'vehicles' => $row->vehicles,
                    'paypal_item' => $row->paypal_item,
                    'services' => array() // Initialize services array
                );
            }
            
            // Check if the service is valid and not already added
            if ($row->id_services) {
                $service_exists = false;
                foreach ($fleets[$row->id_fleet]['services'] as $service) {
                    if ($service['id_service'] == $row->id_services) {
                        $service_exists = true;
                        break;
                    }
                }
                
                if (!$service_exists) {
                    $fleets[$row->id_fleet]['services'][] = array(
                        'id_service' => $row->id_services,
                        'service_name' => $row->service_name,
                        'service_type' => $row->service_type,
                    );
                }
            }
        }
        
        // Return the structured array, re-indexed
        return array_values($fleets);
    }
    
    /**
     * Get API statistics
     * @return array
     */
    public function get_api_stats()
    {
        $stats = array();
        
        // Count zones
        $this->db->from('zones');
        $stats['total_zones'] = $this->db->count_all_results();
        
        // Count hotels
        $this->db->from('hoteles');
        $stats['total_hotels'] = $this->db->count_all_results();
        
        // Count active fleets
        $this->db->where('activo', 1);
        $this->db->from('service_fleets');
        $stats['active_fleets'] = $this->db->count_all_results();
        
        $stats['last_updated'] = date('Y-m-d H:i:s');
        
        return $stats;
    }

    /**
     * Get zone by ID with details
     * @param int $zone_id
     * @return object
     */
    public function get_zone_by_id($zone_id) {
        $this->db->select('z.*, COUNT(h.id_hotel) as hotel_count');
        $this->db->from('zones z');
        $this->db->join('hoteles h', 'h.zones_idzones = z.idzones', 'left');
        $this->db->where('z.idzones', $zone_id);
        $this->db->group_by('z.idzones');
        $query = $this->db->get();
        
        return $query->row();
    }

    /**
     * Get popular zones (most booked)
     * @param int $limit
     * @return array
     */
    public function get_popular_zones($limit = 5) {
        $this->db->select('z.idzones, z.nombre as zone_name, COUNT(r.id_rate) as available_rates');
        $this->db->from('zones z');
        $this->db->join('rates r', 'r.zones_idzones = z.idzones AND r.activo = 1', 'left');
        $this->db->group_by('z.idzones');
        $this->db->order_by('available_rates', 'DESC');
        $this->db->limit($limit);
        $query = $this->db->get();
        
        return $query->result();
    }

    /**
     * Get available fleets for specific zone
     * @param int $zone_id
     * @return array|false
     */
    public function get_fleets_by_zone($zone_id) {
        try {
            // Primero verificar si la zona existe
            $this->db->select('idzones');
            $this->db->from('zones');
            $this->db->where('idzones', $zone_id);
            $zone_exists = $this->db->get();
            
            if ($zone_exists->num_rows() === 0) {
                return false; // Zona no existe
            }
            
            // Buscar flotas disponibles para la zona
            $this->db->select('DISTINCT sf.id_fleet, sf.name, sf.passengers, sf.activo, COUNT(r.id_rate) as rate_count');
            $this->db->from('service_fleets sf');
            $this->db->join('rates r', 'r.service_fleets_id_fleet = sf.id_fleet AND r.activo = 1', 'inner');
            $this->db->where('r.zones_idzones', $zone_id);
            $this->db->where('sf.activo', 1);
            $this->db->group_by('sf.id_fleet, sf.name, sf.passengers, sf.activo');
            $this->db->order_by('sf.name', 'ASC');
            $query = $this->db->get();
            
            if ($this->db->error()['code'] !== 0) {
                log_message('error', 'Database error in get_fleets_by_zone: ' . $this->db->error()['message']);
                return array(); // Error de base de datos
            }
            
            $results = $query->result();
            
            // Si no hay flotas disponibles para la zona, devolver null para distinguir de error
            if (empty($results)) {
                return null; // Zona existe pero no tiene flotas
            }
            
            return $results;
            
        } catch (Exception $e) {
            log_message('error', 'Exception in get_fleets_by_zone: ' . $e->getMessage());
            return array(); // Error de excepción
        }
    }

    /**
     * Save transportation booking data
     * @param array $booking_data
     * @return int|false - Returns the insert ID or false on failure
     */
    /**
 * Save transportation booking data
 * @param array $booking_data
 * @return int|false - Returns the insert ID or false on failure
 */
    public function save_trans_booking($booking_data) {
        try {
            // Map the booking data to database fields (same as Transportation_model)
            $data = array(
                'booking_id' => $booking_data['transaction_id'],
                'name' => $booking_data['firstName'],
                'lastname' => $booking_data['lastName'],
                'email' => $booking_data['email'],
                'phone' => $booking_data['phone_pax_full'],
                'total_fare' => $booking_data['subtotal'],
                'payment_method' => $booking_data['payment_method'],
                'service' => $booking_data['fleet']['name'],
                'type_service' => $booking_data['roundtrip'] ? 'Round Trip' : 'One Way',
                'adults' => $booking_data['adults'],
                'kids' => isset($booking_data['kids']) ? $booking_data['kids'] : 0,
                'created_on' => date('Y-m-d H:i:s'),
                'arrival_from' => $booking_data['from'],
                'arrival_to' => $booking_data['to'],
                'arrival_airline' => $booking_data['arrivalAirline'],
                'arrival_flight' => $booking_data['arrivalFlight'],
                'arrival_time' => $booking_data['arrivalTime'],
                'arrival_date' => $booking_data['arrivalDate'],
                'comments' => !empty($booking_data['specialInstructions']) ? $booking_data['specialInstructions'] : '',
                'car_seats' => isset($booking_data['carSeats']) ? $booking_data['carSeats'] : 0,
                'booster_seats' => isset($booking_data['boosterSeats']) ? $booking_data['boosterSeats'] : 0,
                'baby_seats' => !empty($booking_data['babySeats']) ? $booking_data['babySeats'] : 0,
                'grocery_stop_subtotal' => $booking_data['add_grocery_stop'] ? $booking_data['grocery_stop'] : 0,
                'golf_clubs_bags_qty' => $booking_data['add_golf_clubs_bags'] && isset($booking_data['golfClubsBags']) ? $booking_data['golfClubsBags'] : 0,
                'golf_clubs_bags_price' => $booking_data['add_golf_clubs_bags'] ? $booking_data['golf_clubs_bags'] : 0,
                'surfboards_qty' => $booking_data['add_surfboards'] && isset($booking_data['surfBoards']) ? $booking_data['surfBoards'] : 0,
                'surfboards_price' => $booking_data['add_surfboards'] ? $booking_data['surfboards'] : 0
            );

            // Add roundtrip fields if applicable
            if ($booking_data['roundtrip']) {
                $data['departure_from'] = $booking_data['fromdep'];
                $data['departure_to'] = $booking_data['todep'];
                $data['departure_airline'] = $booking_data['departureAirline'];
                $data['departure_flight'] = $booking_data['departureFlight'];
                $data['departure_time'] = $booking_data['departureTime'];
                $data['departure_date'] = $booking_data['departureDate'];
                $data['departure_pickup'] = $booking_data['departurePickup'];
            }

            $this->db->insert('retails', $data);
            $insert_id = $this->db->insert_id();
            
            if ($insert_id) {
                log_message('info', 'Booking saved successfully with ID: ' . $insert_id);
                return $insert_id;
            } else {
                log_message('error', 'Failed to get insert ID after booking save');
                return false;
            }
            
        } catch (Exception $e) {
            log_message('error', 'Exception in save_trans_booking: ' . $e->getMessage());
            log_message('error', 'Booking data: ' . json_encode($booking_data));
            return false;
        }
    }


}
